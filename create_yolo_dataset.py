#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO数据集分割脚本
将手掌数据集按照8:2比例分割为训练集和测试集
"""

import os
import shutil
import random
from pathlib import Path
import yaml

def get_image_files(source_dir):
    """获取所有图像文件"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.JPG', '.JPEG', '.PNG', '.BMP', '.TIFF'}
    image_files = []
    
    for file_path in Path(source_dir).iterdir():
        if file_path.is_file() and file_path.suffix in image_extensions:
            image_files.append(file_path.stem)  # 返回不带扩展名的文件名
    
    return image_files

def create_directory_structure(output_dir):
    """创建YOLO标准目录结构"""
    dirs_to_create = [
        'images/train',
        'images/val', 
        'labels/train',
        'labels/val'
    ]
    
    for dir_path in dirs_to_create:
        full_path = Path(output_dir) / dir_path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {full_path}")

def copy_files(file_list, source_dir, output_dir, split_type):
    """复制图像和标签文件到目标目录"""
    images_copied = 0
    labels_copied = 0
    
    for filename in file_list:
        # 查找对应的图像文件（支持多种扩展名）
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.JPG', '.JPEG', '.PNG', '.BMP', '.TIFF']
        source_image = None
        
        for ext in image_extensions:
            potential_image = Path(source_dir) / f"{filename}{ext}"
            if potential_image.exists():
                source_image = potential_image
                break
        
        if source_image is None:
            print(f"警告: 找不到图像文件 {filename}")
            continue
            
        # 复制图像文件
        target_image = Path(output_dir) / 'images' / split_type / source_image.name
        shutil.copy2(source_image, target_image)
        images_copied += 1
        
        # 复制标签文件
        source_label = Path(source_dir) / 'yolo_labels' / f"{filename}.txt"
        if source_label.exists():
            target_label = Path(output_dir) / 'labels' / split_type / f"{filename}.txt"
            shutil.copy2(source_label, target_label)
            labels_copied += 1
        else:
            print(f"警告: 找不到标签文件 {filename}.txt")
    
    print(f"{split_type}集: 复制了 {images_copied} 个图像文件, {labels_copied} 个标签文件")
    return images_copied, labels_copied

def create_data_yaml(output_dir, class_names=None):
    """创建data.yaml配置文件"""
    if class_names is None:
        # 默认类别名称，可以根据实际情况修改
        class_names = ['hand']  # 假设只有一个类别：手掌
    
    data_config = {
        'train': 'images/train',
        'val': 'images/val',
        'nc': len(class_names),  # 类别数量
        'names': class_names
    }
    
    yaml_path = Path(output_dir) / 'data.yaml'
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"创建配置文件: {yaml_path}")

def main():
    # 配置参数
    source_dir = "手掌-1"  # 源数据目录
    output_dir = "yolo_dataset"  # 输出目录
    train_ratio = 0.8  # 训练集比例
    random_seed = 42  # 随机种子，确保结果可重现
    
    # 设置随机种子
    random.seed(random_seed)
    
    print("开始创建YOLO数据集...")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    print(f"训练集比例: {train_ratio:.1%}")
    print(f"测试集比例: {1-train_ratio:.1%}")
    print("-" * 50)
    
    # 检查源目录是否存在
    if not Path(source_dir).exists():
        print(f"错误: 源目录 '{source_dir}' 不存在!")
        return
    
    # 获取所有图像文件
    image_files = get_image_files(source_dir)
    print(f"找到 {len(image_files)} 个图像文件")
    
    if len(image_files) == 0:
        print("错误: 没有找到任何图像文件!")
        return
    
    # 随机打乱文件列表
    random.shuffle(image_files)
    
    # 计算分割点
    train_count = int(len(image_files) * train_ratio)
    
    # 分割数据
    train_files = image_files[:train_count]
    val_files = image_files[train_count:]
    
    print(f"训练集: {len(train_files)} 个文件")
    print(f"验证集: {len(val_files)} 个文件")
    print("-" * 50)
    
    # 创建目录结构
    create_directory_structure(output_dir)
    print("-" * 50)
    
    # 复制文件
    print("复制训练集文件...")
    train_images, train_labels = copy_files(train_files, source_dir, output_dir, 'train')
    
    print("复制验证集文件...")
    val_images, val_labels = copy_files(val_files, source_dir, output_dir, 'val')
    
    print("-" * 50)
    
    # 创建配置文件
    create_data_yaml(output_dir)
    
    # 输出总结
    print("数据集创建完成!")
    print(f"总计:")
    print(f"  训练集: {train_images} 图像, {train_labels} 标签")
    print(f"  验证集: {val_images} 图像, {val_labels} 标签")
    print(f"  总计: {train_images + val_images} 图像, {train_labels + val_labels} 标签")
    print(f"\n数据集保存在: {Path(output_dir).absolute()}")

if __name__ == "__main__":
    main()
